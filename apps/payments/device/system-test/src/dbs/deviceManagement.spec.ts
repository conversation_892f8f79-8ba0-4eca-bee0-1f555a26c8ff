import { DynamodbClient, retry } from '@npco/bff-systemtest-utils';
import { Source, StandInField, StandInOperation } from '@npco/component-dto-core';
import {
  PosMethod,
  PosMode,
  Theme,
  ZellerPosFavouriteType,
  type DeviceCreatedEventDto,
  type DeviceInfoUpdateDto,
} from '@npco/component-dto-device';
import { CardScheme } from '@npco/component-dto-transaction';

import gql from 'graphql-tag';
import { v4 } from 'uuid';

import { testIf } from '../testIf';

import { DbsApiTestHelper } from './dbsApiTestHelper';

import { region } from '@npco/bff-systemtest-utils/dist/globalVariables';

describe('device management api tests', () => {
  const apiTestHelper = new DbsApiTestHelper();

  const deviceId = v4();
  const documentClient = new DynamodbClient(region);

  let appsyncApiKeyClient: any;
  let deviceUuid: string;

  beforeAll(async () => {
    await apiTestHelper.beforeAll();
    appsyncApiKeyClient = await apiTestHelper.getApiKeyClient();
    deviceUuid = v4();
    console.log('device', deviceUuid);
  });

  describe('device information tests', () => {
    const deviceInfo: DeviceInfoUpdateDto = {
      id: deviceUuid,
      timestamp: '2022-03-31T09:53:46.000Z',
      timestampUtc: '2022-03-31T09:53:46.000Z',
      model: 'NEW9220',
      serial: '9220103269',
      heightPixels: '1280',
      widthPixels: '720',
      ydpi: '160.157',
      xdpi: '160.421',
      density: '2.0',
      densityDpi: '320',
      androidOs: '7.1.2 - 3.10.49 - eng.buildpl.20220331.095346',
      androidKernel: '3.10.49',
      androidBuild: 'TODO',
      androidDevice: '25',
      androidModel: 'TODO',
      ramTotal: '1914',
      ramAvail: '1208',
      flashTotal: '4060',
      flashAvail: '3326',
      emvL1Version:
        'EMV Contact Level 1 = V1.0\\nEMV Contact Level 2 = V4.3f\\nEMV Contactless Level 1 = V1.0\\nMasterCard PayPass = V3.1.1\\nAmex ExpressPay = V3.1\\nJCB JSpeedy = V1.3\\nDiscover DPAS = V1.0\\nVisa PayWave = V2.1.3\\nQPBOC = V3.0',
      emvL2Version:
        'EMV Contact Level 1 = V1.0\\nEMV Contact Level 2 = V4.3f\\nEMV Contactless Level 1 = V1.0\\nMasterCard PayPass = V3.1.1\\nAmex ExpressPay = V3.1\\nJCB JSpeedy = V1.3\\nDiscover DPAS = V1.0\\nVisa PayWave = V2.1.3\\nQPBOC = V3.0',
      hardwareVersion: '11',
      firmwareVersion: '7.0.20',
      pciFirmwareVersion: '2.3-070020-Debug',
      softwareVersion: '1.4.17-18438-DEBUG',
      securityVersion: 'version="2.0.2" author="buildplat" time="2022-03-31"\n',
      secureCpuId: '661007',
      customerName: 'normal',
      customerId: '0',
      numMasterKeys: 1,
      numDukptKeys: 1,
      ksn: 'FFFF9876543210E000C6',
      numCapk: 0,
      numAids: 0,
      pedCurrentTemp: 29,
      pedModel: 'PCI-PED',
      pedVersion: '4.0.0',
      pedStatus: 1,
      simState: 5,
      simSubscriberId: '505025920295889',
      simSerialNumber: '8961025921439158000',
      simCountry: 'au',
      simOperator: '50502',
      simOperatorName: 'YES OPTUS',
      simNetwork: '50502',
      simNetworkName: 'YES OPTUS',
      simNetworkType: '13',
      simCellDetails:
        'ci:38328378,tac:53290,dbm:-82,asu:58,ci:2147483647,tac:2147483647,dbm:-86,asu:54,ci:2147483647,tac:2147483647,dbm:-72,asu:68,ci:2147483647,tac:2147483647,dbm:-78,asu:62',
      simDns1Address: 'simDns1Address',
      simDns2Address: 'simDns2Address',
      simGatewayAddress: 'simGatewayAddress',
      simIpAddress: 'simIpAddress',
      simRssi: -60,
      wifiDns1Address: 'wifiDns1Address',
      wifiDns2Address: 'wifiDns2Address',
      wifiGatewayAddress: 'wifiGatewayAddress',
      wifiIpAddress: 'wifiIpAddress',
      wifiMaxTransferSpeed: 15,
      wifiStandard: 'wifiStandard',
      wifiTransferSpeed: 14,
      wifiState: 9,
      wifiSsid: 'Zeller-Dev',
      wifiBssid: '94:ff:3c:10:99:43',
      wifiFrequency: '5660',
      wifiChannel: 132,
      wifiSpeed: 72,
      wifiRssi: -58,
      wifiSecurityModel: '[WPA2-PSKFT/PSK-CCMP-preauth][ESS]',
      wifiAvailableNetworks:
        "{ssid:Zeller-Dev,bssid:94:ff:3c:10:99:43,freq:5660,rssi:-60},{ssid:Appliances,bssid:94:ff:3c:ac:9a:91,freq:5825,rssi:-43},{ssid:Zeller-Dev,bssid:94:ff:3c:ac:9a:93,freq:5825,rssi:-43},{ssid:Zeller-HQ,bssid:94:ff:3c:ac:9a:92,freq:5825,rssi:-43},{ssid:Zeller-HQ,bssid:94:ff:3c:10:99:42,freq:5660,rssi:-61},{ssid:Appliances,bssid:94:ff:3c:10:99:41,freq:5660,rssi:-61},{ssid:Zeller-Dev,bssid:94:ff:3c:ac:96:13,freq:5540,rssi:-67},{ssid:Zeller-HQ,bssid:94:ff:3c:ac:96:12,freq:5540,rssi:-67},{ssid:Appliances,bssid:94:ff:3c:ac:96:11,freq:5540,rssi:-67},{ssid:Appliances,bssid:94:ff:3c:ac:9a:81,freq:2462,rssi:-40},{ssid:Zeller-Dev,bssid:94:ff:3c:ac:9a:82,freq:2462,rssi:-40},{ssid:Zeller-Dev,bssid:94:ff:3c:10:99:32,freq:2437,rssi:-58},{ssid:Appliances,bssid:94:ff:3c:10:99:31,freq:2437,rssi:-58},{ssid:Zeller-Dev,bssid:94:ff:3c:ac:96:02,freq:2412,rssi:-62},{ssid:Appliances,bssid:94:ff:3c:ac:96:01,freq:2412,rssi:-62},{ssid:Appliances,bssid:a2:6c:ac:03:bd:51,freq:2412,rssi:-64},{ssid:Zeller-Dev,bssid:90:6c:ac:03:bd:51,freq:2412,rssi:-64},{ssid:Appliances,bssid:94:ff:3c:ac:ba:91,freq:5320,rssi:-71},{ssid:Zeller-HQ,bssid:94:ff:3c:ac:ba:92,freq:5320,rssi:-71},{ssid:Zeller-Dev,bssid:94:ff:3c:ac:ba:93,freq:5320,rssi:-70},{ssid:Zeller-HQ,bssid:94:ff:3c:ac:78:52,freq:5745,rssi:-77},{ssid:Zeller-Dev,bssid:94:ff:3c:ac:78:53,freq:5745,rssi:-76},{ssid:Appliances,bssid:94:ff:3c:ac:78:51,freq:5745,rssi:-77},{ssid:Zeller-HQ,bssid:94:ff:3c:ac:a1:52,freq:5500,rssi:-73},{ssid:Zeller-Dev,bssid:94:ff:3c:ac:a1:53,freq:5500,rssi:-73},{ssid:Appliances,bssid:94:ff:3c:ac:a1:51,freq:5500,rssi:-73},{ssid:Appliances,bssid:94:ff:3c:ac:78:41,freq:2462,rssi:-70},{ssid:Appliances,bssid:94:ff:3c:ac:47:91,freq:5680,rssi:-79},{ssid:Zeller-Dev,bssid:94:ff:3c:ac:47:93,freq:5680,rssi:-79},{ssid:Zeller-HQ,bssid:94:ff:3c:ac:47:92,freq:5680,rssi:-79},{ssid:Appliances,bssid:94:ff:3c:ac:a9:d1,freq:5580,rssi:-83},{ssid:Zeller-HQ,bssid:94:ff:3c:ac:a9:d2,freq:5580,rssi:-82},{ssid:Zeller-Dev,bssid:94:ff:3c:ac:a9:d3,freq:5580,rssi:-82},{ssid:David's Samsung Hotspot,bssid:12:6d:50:c0:86:27,freq:2437,rssi:-66},{ssid:STYX_5G,bssid:02:98:12:a3:6e:a3,freq:5500,rssi:-86},{ssid:Appliances,bssid:b2:6c:ac:03:bd:59,freq:5200,rssi:-60},{ssid:Zeller-Dev,bssid:a2:6c:ac:03:bd:59,freq:5200,rssi:-61},{ssid:Zeller-HQ,bssid:90:6c:ac:03:bd:59,freq:5200,rssi:-61},{ssid:Zeller-Dev,bssid:94:ff:3c:ac:78:42,freq:2462,rssi:-70},{ssid:DIRECT-39451_QL-1110NWB,bssid:92:0f:0c:5f:e1:42,freq:2437,rssi:-71},{ssid:,bssid:62:22:32:22:62:7e,freq:2437,rssi:-72}",
      tcpMac: '54:65:03:E7:6A:15',
      tcpIpAddress: '**********',
      tcpGatewayAddress: '*********',
      tcpDns1Address: '*******',
      tcpDns2Address: '*******',
      tcpDhcp: true,
    };
    it('should throw device information not found when there is no device settiings', async () => {
      await apiTestHelper.sendDeviceProjectionEvent('dbs.Device.InformationUpdated', { ...deviceInfo, id: deviceUuid });
      await retry(async () => {
        await expect(
          (
            await apiTestHelper.getOpenIdClient()
          ).query({
            query: gql`
              query getDeviceInformation($deviceUuid: ID!) {
                getDeviceInformation(deviceUuid: $deviceUuid) {
                  id
                  network {
                    cellularInfo {
                      operatorName
                      ipAddress
                      state
                      strength
                    }
                    wifiInfo {
                      ssid
                      ipAddress
                      macAddress
                      strength
                    }
                    ethernetInfo {
                      ipAddress
                      macAddress
                    }
                  }
                }
              }
            `,
            variables: {
              deviceUuid,
            },
          }),
        ).rejects.toThrow('Device information not found');
      });
    });
    it('should be able to get device information', async () => {
      const deviceSettings: DeviceCreatedEventDto = {
        deviceUuid,
        entityUuid: apiTestHelper.getEntityUuid(),
        name: 'zeller device name 1',
        entity: {
          canAcquire: true,
          canAcquireMoto: true,
          canAcquireMobile: true,
          canRefund: true,
          canStandIn: true,
          domicile: 'AUS',
          currency: 'AUD',
        },
        terminalConfig: 'terminalConfig',
        emvConfig: 'emvConfig',
        emvTables: 'emvTables',
        emvCaKeys: 'emvCaKeys',
        geofencing: 'geofencing',
        receipt: {
          merchantCopy: true,
          name: 'String',
          address1: 'String',
          address2: 'String',
          number: 'String',
          phone: 'String',
          email: 'String',
          message: 'String',
          returnsMessage: 'String',
          website: 'String',
          logoMonochrome: 'logoMonochrome',
          instagram: 'String',
          facebook: 'String',
          twitter: 'String',
          logo: 'String',
          printLogo: false,
          printSocials: false,
          printDeclinedTransaction: false,
        },
        moto: {
          enabled: true,
          defaultEntryMethod: true,
          requiresPin: true,
        },
        surchargesTaxes: {
          surchargeEnabled: true,
          surchargePercent: 1,
          feePercent: 1,
          gstEnabled: true,
          gstPercent: 10,
          taxes: [{ percent: 2, name: 'GST' }],
          feePercentMoto: 100,
          surchargeFullFees: false,
          surchargeFullFeesMoto: false,
          surchargePercentMoto: 100,
          feesSurchargeCpoc: {
            surchargePercent: 2,
            feePercent: 2,
            surchargeFullFees: true,
            surchargeEnabled: true,
            feeFixed: 2,
          },
          // feesSurchargeCp: {},
          // feesSurchargeMoto: {},
          feesSurchargeXinv: {
            surchargePercent: 3,
            feePercent: 3,
            surchargeFullFees: true,
            surchargeEnabled: true,
            feeFixed: 3,
            feeFixedIntl: 3,
            feePercentIntl: 3,
            // surchargePercentIntl: 3,
          },
          feesSurchargeZinv: {
            surchargePercent: 4,
            feePercent: 4,
            surchargeFullFees: true,
            surchargeEnabled: true,
            feeFixed: 4,
            feeFixedIntl: 4,
            feePercentIntl: 4,
            // surchargePercentIntl: 4,
          },
        } as any,
        tipping: {
          customTipAllowed: true,
          enabled: true,
          tipPercent1: 5,
          tipPercent2: 4,
          tipPercent3: 3,
        },
        schemes: [{ name: CardScheme.AMEX }],
        schemesMoto: [{ name: CardScheme.MC }],
        standInRules: [
          {
            operation: StandInOperation.BELOW,
            field: StandInField.OFFLINE_AMOUNT,
            value: v4(),
          },
          {
            operation: StandInOperation.ABOVE,
            field: StandInField.TRANSACTION_AMOUNT,
            value: v4(),
          },
        ],

        screen: {
          sleepEnabled: true,
          sleepTimer: 1,
          standbyEnabled: true,
          standbyTimer: 1,
          brightness: 1,
          pinEntryTimer: 1,
          inactivityTimer: 1,
          communicationsTimer: 1,
          notHibernateWhenPluggedIn: false,
          theme: Theme.DARK_THEME,
        },
        network: {
          wifiEnabled: true,
          wifiSsid: '',
          cellularEnabled: true,
          cellularNetwork: 'string',
          ethernetEnabled: true,
        },
        posSettings: {
          posSoftwareName: 'Square',
          mode: PosMode.PAY_AT_TABLE,
          connectionMethod: PosMethod.POS_CONNECTOR,
          active: Source.HL_POS,
          port: 1,
          posReceipt: false,
          ipAddress: 'ipAddress',
          posVenue: {
            id: v4(),
            name: v4(),
            locations: [{ id: v4(), name: v4(), number: v4() }],
          },
          hlSettings: {
            billingSummary: true,
          },
          zellerPosSettings: {
            favourites: [
              { id: v4(), type: ZellerPosFavouriteType.ITEM },
              { id: v4(), type: ZellerPosFavouriteType.CATEGORY },
            ],
          },
        },
        features: {
          declineSoundEnabled: true,
          splitPaymentEnabled: false,
          restrictReportAccessEnabled: true,
        },
      };
      await apiTestHelper.sendDeviceProjectionEvent('dbs.Device.Created', {
        ...deviceSettings,
        model: 'model',
        serial: 'serial',
        firmwareVersion: 'firmwareVersion',
        status: 'ACTIVE',
        appVersion: 'appVersion',
      });
      await apiTestHelper.sendDeviceProjectionEvent('dbs.Device.InformationUpdated', { ...deviceInfo, id: deviceUuid });
      await retry(async () => {
        const info = await (
          await apiTestHelper.getOpenIdClient()
        ).query({
          query: gql`
            query getDeviceInformation($deviceUuid: ID!) {
              getDeviceInformation(deviceUuid: $deviceUuid) {
                id
                network {
                  cellularInfo {
                    operatorName
                    ipAddress
                    state
                    strength
                  }
                  wifiInfo {
                    ssid
                    ipAddress
                    macAddress
                    strength
                  }
                  ethernetInfo {
                    ipAddress
                    macAddress
                  }
                }
              }
            }
          `,
          variables: {
            deviceUuid,
          },
        });
        expect(info.data.getDeviceInformation).toEqual({
          id: deviceUuid,
          network: {
            cellularInfo: {
              operatorName: 'YES OPTUS',
              ipAddress: 'simIpAddress',
              state: '5',
              strength: '-60',
            },
            wifiInfo: {
              ssid: 'Zeller-Dev',
              ipAddress: 'wifiIpAddress',
              macAddress: null,
              strength: '-58',
            },
            ethernetInfo: {
              ipAddress: '**********',
              macAddress: '54:65:03:E7:6A:15',
            },
          },
        });
      });
    });
  });

  describe('device checkSoftwareUpdate tests', () => {
    it('should get NOT_FOUND error when software update records not set for device uuid', async () => {
      const targetVersion = {
        appUrl: 's3://apk',
        appVersion: '7.0.10',
        firmwareUrl: 's3://img',
        firmwareVersion: 'v7',
      };

      await appsyncApiKeyClient

        .query({
          query: gql`
            query checkSoftwareUpdate($actualVersion: CheckSoftwareUpdateInput) {
              checkSoftwareUpdate(input: $actualVersion) {
                app {
                  version
                  url
                }
                firmware {
                  version
                  url
                }
              }
            }
          `,
          variables: {
            actualVersion: {
              id: deviceUuid,
              appVersion: targetVersion.appVersion,
              firmwareVersion: targetVersion.firmwareVersion,
            },
          },
        })
        .then(() => fail())
        .catch((err: any) => {
          console.info(JSON.stringify(err));
          expect(err.graphQLErrors[0].errorType).toEqual('NOT_FOUND');
        });
    });

    it('should be able to check if software update exist for device uuid', async () => {
      const targetVersion = {
        forceUpdate: false,
        appUrl: 's3://apk',
        appVersion: '1.3.25',
        firmwareUrl: 's3://img',
        firmwareVersion: '1.3.25',
      };
      deviceUuid = apiTestHelper.getDeviceUuid();

      await documentClient.put({
        TableName: `dev-dbs-api-dynamodb-Devices`,
        Item: {
          id: deviceUuid,
          type: 'device.software',
          deviceUuid,
          ...targetVersion,
        },
      });

      await appsyncApiKeyClient

        .query({
          query: gql`
            query checkSoftwareUpdate($actualVersion: CheckSoftwareUpdateInput) {
              checkSoftwareUpdate(input: $actualVersion) {
                forceUpdate
                app {
                  version
                  url
                }
                firmware {
                  version
                  url
                }
                additionalApps {
                  name
                  version
                  url
                }
              }
            }
          `,
          variables: {
            actualVersion: {
              id: deviceUuid,
              appVersion: targetVersion.appVersion,
              firmwareVersion: targetVersion.firmwareVersion,
            },
          },
        })
        .then(async ({ data }: any) => {
          expect(data.checkSoftwareUpdate.app).toEqual(null);
          expect(data.checkSoftwareUpdate.firmware).toEqual(null);
          expect(data.checkSoftwareUpdate.additionalApps).toEqual(null);
          expect(data.checkSoftwareUpdate.forceUpdate).toEqual(false);
        })
        .catch((err: any) => {
          console.error(err);
          throw err;
        });
    });

    testIf(
      apiTestHelper.isDev(),
      'should receive update in onSoftwareUpdateRequired subscription without additionalApps',
      async () => {
        console.log('subscribe to device uuid:', deviceId);
        await documentClient.put({
          TableName: `dev-dbs-api-dynamodb-Devices`,
          Item: {
            id: deviceId,
            type: 'device.software',
            deviceUuid: deviceId,
            forceUpdate: false,
            appUrl: 's3://apk',
            appVersion: '1.3.25',
            firmwareUrl: 's3://img',
            firmwareVersion: '1.3.25',
          },
        });
        await documentClient.put({
          TableName: `dev-dbs-api-dynamodb-Devices`,
          Item: {
            id: deviceId,
            type: 'device.core',
            status: 'ACTIVE',
          },
        });

        await apiTestHelper.sleep(1000);

        const newAppVersion = `1.3.27+${Date.now()}`;
        console.info('subscribing on deviceUuid:', deviceId);
        await new Promise(async (resolve) => {
          const subscriber = (await apiTestHelper.getApiKeyClient())
            .subscribe({
              query: gql`
                subscription onSoftwareUpdateRequired($deviceUuid: ID!) {
                  onSoftwareUpdateRequired(id: $deviceUuid) {
                    id
                    forceUpdate
                    app {
                      url
                      version
                    }
                    firmware {
                      url
                      version
                    }
                    additionalApps {
                      name
                      version
                      url
                    }
                  }
                }
              `,
              variables: {
                deviceUuid: deviceId,
              },
            })
            .subscribe({
              next: ({ data }: any) => {
                console.log('get software update data:', data);
                const { onSoftwareUpdateRequired } = data;
                expect(onSoftwareUpdateRequired.forceUpdate).toEqual(false);
                expect(onSoftwareUpdateRequired.app.version).toEqual(newAppVersion);
                subscriber.unsubscribe();
                resolve(null);
              },
              error: (error: any) => console.error('get error on subscription:', error),
            });

          await apiTestHelper.sleep();
          console.info('update device.software appVersion=', newAppVersion);
          await documentClient.update({
            TableName: `dev-dbs-api-dynamodb-Devices`,
            Key: {
              id: deviceId,
              type: 'device.software',
            },
            UpdateExpression: 'set appVersion = :appVersion',
            ExpressionAttributeValues: {
              ':appVersion': newAppVersion,
            },
          });
        });
      },
      60000,
    );

    testIf(
      apiTestHelper.isDev(),
      'should receive update in onSoftwareUpdateRequired subscription with additionalApps',
      async () => {
        console.log('subscribe to deviceUuid:', deviceId);

        const additionalApps = [
          {
            name: v4(),
            url: v4(),
            version: '1.0.1',
          },
        ];
        await new Promise(async (resolve) => {
          const subscriber = (await apiTestHelper.getApiKeyClient())
            .subscribe({
              query: gql`
                subscription onSoftwareUpdateRequired($deviceUuid: ID!) {
                  onSoftwareUpdateRequired(id: $deviceUuid) {
                    id
                    forceUpdate
                    app {
                      url
                      version
                    }
                    firmware {
                      url
                      version
                    }
                    additionalApps {
                      name
                      version
                      url
                    }
                  }
                }
              `,
              variables: {
                deviceUuid: deviceId,
              },
            })
            .subscribe({
              next: ({ data }: any) => {
                console.log('get software update data:', data);
                const { onSoftwareUpdateRequired } = data;
                expect(onSoftwareUpdateRequired.forceUpdate).toEqual(false);
                expect(onSoftwareUpdateRequired.additionalApps).toEqual(additionalApps);
                subscriber.unsubscribe();
                resolve(null);
              },
              error: (error: any) => console.error('get error on subscription:', error),
            });

          await apiTestHelper.sleep();

          console.info('update device.software additionalApps=', additionalApps);

          await documentClient.update({
            TableName: `dev-dbs-api-dynamodb-Devices`,
            Key: {
              id: deviceId,
              type: 'device.software',
            },
            UpdateExpression: 'set additionalApps = :additionalApps',
            ExpressionAttributeValues: {
              ':additionalApps': additionalApps,
            },
          });
        });
      },
      60000,
    );
  });

  xit('should be able to request key initialisation', async () => {
    const deviceKeysDbItem = {
      tcuid: v4(),
      kbpkSignature: v4(),
      tr31KeyBlock: v4(),
    };
    await documentClient.put({
      TableName: `dev-dbs-api-devices-tmp-DeviceKeysDb`,
      Item: deviceKeysDbItem,
    });

    await appsyncApiKeyClient
      .mutate({
        mutation: gql`
          mutation requestKeyInitialisation($deviceKeys: RequestKeyInitialisationInput!) {
            requestKeyInitialisation(input: $deviceKeys) {
              tr31KeyBlock
            }
          }
        `,
        variables: {
          deviceKeys: {
            tcuid: deviceKeysDbItem.tcuid,
            kbpkSignature: deviceKeysDbItem.kbpkSignature,
          },
        },
      })
      .then(async ({ data }: any) => {
        expect(data.requestKeyInitialisation.tr31KeyBlock).toEqual(deviceKeysDbItem.tr31KeyBlock);
        // expect(data.requestKeyInitialisation.error).toEqual(false);
      })
      .catch((err: any) => {
        console.error(err);
        throw err;
      });
  });
});
